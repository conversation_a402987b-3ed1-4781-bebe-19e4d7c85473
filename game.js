/**
 * Zombie Apocalypse FPS Game
 * A complete first-person shooter zombie game using Three.js
 */

class ZombieGame {
    constructor() {
        // Core Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // Game state
        this.gameState = 'loading'; // loading, menu, playing, paused, gameOver
        this.score = 0;
        this.health = 100;
        this.maxHealth = 100;
        
        // Player properties
        this.player = {
            position: new THREE.Vector3(0, 1.8, 0),
            velocity: new THREE.Vector3(0, 0, 0),
            speed: 5,
            jumpPower: 8,
            onGround: false,
            height: 1.8
        };
        
        // Input handling
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        this.mousePressed = false;
        
        // Weapon system
        this.weapons = {
            pistol: {
                damage: 25,
                ammo: 12,
                maxAmmo: 60,
                fireRate: 300,
                range: 50,
                spread: 0.02,
                bulletsPerShot: 1,
                reloadTime: 1500,
                name: 'Pistol'
            },
            rifle: {
                damage: 35,
                ammo: 30,
                maxAmmo: 90,
                fireRate: 150,
                range: 100,
                spread: 0.01,
                bulletsPerShot: 1,
                reloadTime: 2000,
                name: 'Assault Rifle'
            },
            shotgun: {
                damage: 60,
                ammo: 8,
                maxAmmo: 32,
                fireRate: 800,
                range: 20,
                spread: 0.15,
                bulletsPerShot: 5,
                reloadTime: 2500,
                name: 'Shotgun'
            }
        };
        this.currentWeapon = 'rifle';
        this.lastShotTime = 0;
        this.reloading = false;
        this.weaponModel = null;
        this.muzzleFlash = null;
        
        // Game objects
        this.zombies = [];
        this.bullets = [];
        this.buildings = [];
        this.effects = [];
        this.particles = [];

        // Zombie spawning
        this.zombieSpawnPoints = [];
        this.maxZombies = 15;
        this.zombieSpawnRate = 3000; // milliseconds
        this.lastZombieSpawn = 0;
        
        // Performance
        this.clock = new THREE.Clock();
        this.deltaTime = 0;
        
        // Audio context (will be initialized after user interaction)
        this.audioContext = null;
        this.sounds = {};
        
        this.init();
    }
    
    async init() {
        this.showLoadingProgress(10);
        await this.initThreeJS();
        
        this.showLoadingProgress(30);
        await this.setupControls();
        
        this.showLoadingProgress(50);
        await this.createEnvironment();
        
        this.showLoadingProgress(70);
        await this.initAudio();

        this.showLoadingProgress(80);
        await this.createWeaponModels();

        this.showLoadingProgress(90);
        this.setupEventListeners();
        
        this.showLoadingProgress(100);
        setTimeout(() => {
            this.hideLoading();
            this.showStartScreen();
        }, 500);
    }
    
    async initThreeJS() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x404040, 50, 200);
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.copy(this.player.position);
        
        // Create renderer
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: false
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // Add basic lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
    }
    
    async setupControls() {
        // Initialize PointerLockControls
        this.controls = new THREE.PointerLockControls(this.camera, document.body);
        this.scene.add(this.controls.getObject());
        
        // Set initial position
        this.controls.getObject().position.copy(this.player.position);
    }
    
    async createEnvironment() {
        // Create ground with texture
        await this.createGround();

        // Create city streets
        this.createStreets();

        // Create detailed buildings
        this.createDetailedBuildings();

        // Add atmospheric effects
        this.createAtmosphere();

        // Add debris and props
        this.createDebris();

        // Enhance lighting for post-apocalyptic feel
        this.enhanceLighting();

        // Setup zombie spawn points
        this.setupZombieSpawnPoints();
    }

    async createGround() {
        const groundGeometry = new THREE.PlaneGeometry(300, 300, 50, 50);

        // Create a more detailed ground material
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x2a2a2a,
            transparent: false
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;

        // Add some vertex displacement for uneven ground
        const vertices = ground.geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            vertices[i + 2] += (Math.random() - 0.5) * 0.5; // Y displacement
        }
        ground.geometry.attributes.position.needsUpdate = true;
        ground.geometry.computeVertexNormals();

        this.scene.add(ground);
    }

    createStreets() {
        const streetMaterial = new THREE.MeshLambertMaterial({ color: 0x1a1a1a });

        // Main street (North-South)
        const mainStreetGeometry = new THREE.PlaneGeometry(12, 300);
        const mainStreet = new THREE.Mesh(mainStreetGeometry, streetMaterial);
        mainStreet.rotation.x = -Math.PI / 2;
        mainStreet.position.y = 0.01;
        mainStreet.receiveShadow = true;
        this.scene.add(mainStreet);

        // Cross street (East-West)
        const crossStreetGeometry = new THREE.PlaneGeometry(300, 12);
        const crossStreet = new THREE.Mesh(crossStreetGeometry, streetMaterial);
        crossStreet.rotation.x = -Math.PI / 2;
        crossStreet.position.y = 0.01;
        crossStreet.receiveShadow = true;
        this.scene.add(crossStreet);

        // Add street markings
        this.createStreetMarkings();
    }

    createStreetMarkings() {
        const markingMaterial = new THREE.MeshBasicMaterial({ color: 0x444444 });

        // Center line markings
        for (let i = -140; i < 140; i += 20) {
            const markingGeometry = new THREE.PlaneGeometry(1, 8);
            const marking = new THREE.Mesh(markingGeometry, markingMaterial);
            marking.rotation.x = -Math.PI / 2;
            marking.position.set(0, 0.02, i);
            this.scene.add(marking);
        }
    }

    createDetailedBuildings() {
        // Building materials with post-apocalyptic colors
        const buildingMaterials = [
            new THREE.MeshLambertMaterial({ color: 0x4a4a4a }), // Dark gray
            new THREE.MeshLambertMaterial({ color: 0x5a4a3a }), // Brown
            new THREE.MeshLambertMaterial({ color: 0x3a3a4a }), // Blue-gray
            new THREE.MeshLambertMaterial({ color: 0x4a3a3a }), // Dark red
        ];

        // Create city blocks
        this.createCityBlock(-80, -80, buildingMaterials);
        this.createCityBlock(20, -80, buildingMaterials);
        this.createCityBlock(-80, 20, buildingMaterials);
        this.createCityBlock(20, 20, buildingMaterials);

        // Add some isolated buildings
        this.createIsolatedBuildings(buildingMaterials);
    }

    createCityBlock(startX, startZ, materials) {
        const blockSize = 80;
        const buildingSpacing = 20;

        for (let x = 0; x < 3; x++) {
            for (let z = 0; z < 3; z++) {
                const posX = startX + x * buildingSpacing + Math.random() * 5;
                const posZ = startZ + z * buildingSpacing + Math.random() * 5;

                this.createDetailedBuilding(posX, posZ, materials);
            }
        }
    }

    createDetailedBuilding(x, z, materials) {
        const width = Math.random() * 8 + 6;
        const height = Math.random() * 25 + 15;
        const depth = Math.random() * 8 + 6;

        // Main building structure
        const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
        const material = materials[Math.floor(Math.random() * materials.length)];
        const building = new THREE.Mesh(buildingGeometry, material);

        building.position.set(x, height / 2, z);
        building.castShadow = true;
        building.receiveShadow = true;

        this.scene.add(building);
        this.buildings.push(building);

        // Add building details
        this.addBuildingDetails(building, width, height, depth);
    }

    addBuildingDetails(building, width, height, depth) {
        const pos = building.position;

        // Add windows (dark rectangles)
        const windowMaterial = new THREE.MeshBasicMaterial({ color: 0x111111 });
        const windowsPerFloor = Math.floor(width / 3);
        const floors = Math.floor(height / 4);

        for (let floor = 1; floor < floors; floor++) {
            for (let win = 0; win < windowsPerFloor; win++) {
                // Front windows
                const windowGeometry = new THREE.PlaneGeometry(1.5, 2);
                const window = new THREE.Mesh(windowGeometry, windowMaterial);
                window.position.set(
                    pos.x - width/2 + (win + 1) * (width / (windowsPerFloor + 1)),
                    pos.y - height/2 + floor * 4,
                    pos.z + depth/2 + 0.01
                );
                this.scene.add(window);

                // Randomly add broken/lit windows
                if (Math.random() < 0.1) {
                    const lightMaterial = new THREE.MeshBasicMaterial({
                        color: 0xffaa00,
                        transparent: true,
                        opacity: 0.7
                    });
                    const lightWindow = new THREE.Mesh(windowGeometry, lightMaterial);
                    lightWindow.position.copy(window.position);
                    lightWindow.position.z += 0.01;
                    this.scene.add(lightWindow);
                }
            }
        }

        // Add rooftop details
        if (Math.random() < 0.5) {
            this.addRooftopDetails(pos, width, height, depth);
        }
    }

    addRooftopDetails(pos, width, height, depth) {
        // Add antenna or water tank
        const detailMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        if (Math.random() < 0.5) {
            // Antenna
            const antennaGeometry = new THREE.CylinderGeometry(0.1, 0.1, 8);
            const antenna = new THREE.Mesh(antennaGeometry, detailMaterial);
            antenna.position.set(pos.x, pos.y + height/2 + 4, pos.z);
            this.scene.add(antenna);
        } else {
            // Water tank
            const tankGeometry = new THREE.CylinderGeometry(2, 2, 3);
            const tank = new THREE.Mesh(tankGeometry, detailMaterial);
            tank.position.set(pos.x, pos.y + height/2 + 1.5, pos.z);
            this.scene.add(tank);
        }
    }

    createIsolatedBuildings(materials) {
        // Add some buildings outside the main blocks
        for (let i = 0; i < 8; i++) {
            let x, z;
            do {
                x = (Math.random() - 0.5) * 200;
                z = (Math.random() - 0.5) * 200;
            } while (Math.abs(x) < 15 || Math.abs(z) < 15); // Avoid streets

            this.createDetailedBuilding(x, z, materials);
        }
    }

    createAtmosphere() {
        // Add fog for atmospheric effect
        this.scene.fog = new THREE.Fog(0x404040, 30, 150);

        // Add particle system for dust/smoke
        this.createDustParticles();

        // Add some ambient fire effects
        this.createFireEffects();
    }

    createDustParticles() {
        const particleCount = 200;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 200;     // x
            positions[i + 1] = Math.random() * 50;          // y
            positions[i + 2] = (Math.random() - 0.5) * 200; // z
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const particleMaterial = new THREE.PointsMaterial({
            color: 0x888888,
            size: 0.5,
            transparent: true,
            opacity: 0.3,
            blending: THREE.AdditiveBlending
        });

        const dustSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(dustSystem);

        // Store for animation
        this.dustParticles = dustSystem;
    }

    createFireEffects() {
        // Add some burning barrels and fire effects
        for (let i = 0; i < 5; i++) {
            const x = (Math.random() - 0.5) * 150;
            const z = (Math.random() - 0.5) * 150;

            // Skip if too close to streets
            if (Math.abs(x) < 20 && Math.abs(z) < 20) continue;

            this.createBurningBarrel(x, z);
        }
    }

    createBurningBarrel(x, z) {
        // Create barrel
        const barrelGeometry = new THREE.CylinderGeometry(1, 1, 2);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.position.set(x, 1, z);
        barrel.castShadow = true;
        this.scene.add(barrel);

        // Create fire effect (simple orange glow)
        const fireGeometry = new THREE.CylinderGeometry(0.8, 0.5, 1.5);
        const fireMaterial = new THREE.MeshBasicMaterial({
            color: 0xff4400,
            transparent: true,
            opacity: 0.7,
            blending: THREE.AdditiveBlending
        });
        const fire = new THREE.Mesh(fireGeometry, fireMaterial);
        fire.position.set(x, 2.5, z);
        this.scene.add(fire);

        // Add point light for fire
        const fireLight = new THREE.PointLight(0xff4400, 1, 10);
        fireLight.position.set(x, 3, z);
        this.scene.add(fireLight);

        // Store fire for animation
        if (!this.fireEffects) this.fireEffects = [];
        this.fireEffects.push({ fire, light: fireLight });
    }

    createDebris() {
        // Add scattered debris around the city
        const debrisMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        for (let i = 0; i < 50; i++) {
            const x = (Math.random() - 0.5) * 180;
            const z = (Math.random() - 0.5) * 180;

            // Create random debris shapes
            let debrisGeometry;
            const debrisType = Math.random();

            if (debrisType < 0.3) {
                // Box debris
                debrisGeometry = new THREE.BoxGeometry(
                    Math.random() * 2 + 0.5,
                    Math.random() * 1 + 0.2,
                    Math.random() * 2 + 0.5
                );
            } else if (debrisType < 0.6) {
                // Cylinder debris
                debrisGeometry = new THREE.CylinderGeometry(
                    Math.random() * 0.5 + 0.2,
                    Math.random() * 0.5 + 0.2,
                    Math.random() * 2 + 0.5
                );
            } else {
                // Sphere debris
                debrisGeometry = new THREE.SphereGeometry(Math.random() * 0.8 + 0.3);
            }

            const debris = new THREE.Mesh(debrisGeometry, debrisMaterial);
            debris.position.set(x, 0.5, z);
            debris.rotation.set(
                Math.random() * Math.PI,
                Math.random() * Math.PI,
                Math.random() * Math.PI
            );
            debris.castShadow = true;
            this.scene.add(debris);
        }

        // Add some abandoned cars
        this.createAbandonedCars();
    }

    createAbandonedCars() {
        for (let i = 0; i < 8; i++) {
            const x = (Math.random() - 0.5) * 160;
            const z = (Math.random() - 0.5) * 160;

            // Car body
            const carBodyGeometry = new THREE.BoxGeometry(4, 1.5, 2);
            const carMaterial = new THREE.MeshLambertMaterial({
                color: Math.random() < 0.5 ? 0x444444 : 0x666666
            });
            const carBody = new THREE.Mesh(carBodyGeometry, carMaterial);
            carBody.position.set(x, 0.75, z);
            carBody.castShadow = true;
            this.scene.add(carBody);

            // Car roof
            const roofGeometry = new THREE.BoxGeometry(3, 1, 1.8);
            const roof = new THREE.Mesh(roofGeometry, carMaterial);
            roof.position.set(x, 1.75, z);
            roof.castShadow = true;
            this.scene.add(roof);

            // Wheels
            const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3);
            const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });

            const wheelPositions = [
                [x - 1.3, 0.4, z - 0.8],
                [x + 1.3, 0.4, z - 0.8],
                [x - 1.3, 0.4, z + 0.8],
                [x + 1.3, 0.4, z + 0.8]
            ];

            wheelPositions.forEach(pos => {
                const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
                wheel.position.set(...pos);
                wheel.rotation.z = Math.PI / 2;
                this.scene.add(wheel);
            });
        }
    }

    enhanceLighting() {
        // Adjust existing directional light for post-apocalyptic feel
        const existingLight = this.scene.children.find(child => child instanceof THREE.DirectionalLight);
        if (existingLight) {
            existingLight.color.setHex(0xffddaa); // Warmer, dusty light
            existingLight.intensity = 0.6;
        }

        // Add some street lights (mostly broken)
        this.createStreetLights();

        // Add emergency lighting
        this.createEmergencyLights();
    }

    createStreetLights() {
        const streetLightPositions = [
            [-30, 0, -60], [30, 0, -60],
            [-30, 0, 0], [30, 0, 0],
            [-30, 0, 60], [30, 0, 60],
            [0, 0, -30], [0, 0, 30]
        ];

        streetLightPositions.forEach((pos, index) => {
            // Light pole
            const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8);
            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(pos[0], 4, pos[2]);
            this.scene.add(pole);

            // Light fixture
            const fixtureGeometry = new THREE.SphereGeometry(0.5);
            const fixtureMaterial = new THREE.MeshBasicMaterial({ color: 0x444444 });
            const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
            fixture.position.set(pos[0], 7.5, pos[2]);
            this.scene.add(fixture);

            // Only some lights work (flickering effect)
            if (Math.random() < 0.3) {
                const light = new THREE.PointLight(0xffffaa, 0.5, 15);
                light.position.set(pos[0], 7.5, pos[2]);
                this.scene.add(light);

                // Store for flickering animation
                if (!this.streetLights) this.streetLights = [];
                this.streetLights.push(light);
            }
        });
    }

    createEmergencyLights() {
        // Add some emergency/warning lights on buildings
        for (let i = 0; i < 3; i++) {
            const building = this.buildings[Math.floor(Math.random() * this.buildings.length)];
            if (!building) continue;

            const emergencyLight = new THREE.PointLight(0xff0000, 0.8, 20);
            emergencyLight.position.set(
                building.position.x,
                building.position.y + 5,
                building.position.z
            );
            this.scene.add(emergencyLight);

            // Store for blinking animation
            if (!this.emergencyLights) this.emergencyLights = [];
            this.emergencyLights.push(emergencyLight);
        }
    }

    setupZombieSpawnPoints() {
        // Create spawn points around the perimeter of the city
        const spawnDistance = 80;
        const spawnCount = 12;

        for (let i = 0; i < spawnCount; i++) {
            const angle = (i / spawnCount) * Math.PI * 2;
            const x = Math.cos(angle) * spawnDistance;
            const z = Math.sin(angle) * spawnDistance;

            this.zombieSpawnPoints.push(new THREE.Vector3(x, 0, z));
        }
    }

    createZombie(position, type = 'walker') {
        const zombieGroup = new THREE.Group();

        // Zombie types with different properties
        const zombieTypes = {
            walker: {
                speed: 2,
                health: 50,
                damage: 15,
                color: 0x4a5a4a,
                scale: 1,
                attackRange: 2
            },
            runner: {
                speed: 6,
                health: 30,
                damage: 10,
                color: 0x5a4a4a,
                scale: 0.9,
                attackRange: 1.5
            },
            brute: {
                speed: 1,
                health: 100,
                damage: 25,
                color: 0x6a4a4a,
                scale: 1.3,
                attackRange: 2.5
            }
        };

        const zombieData = zombieTypes[type];

        // Create zombie body
        const bodyGeometry = new THREE.CapsuleGeometry(0.3, 1.4);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: zombieData.color });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.9;
        zombieGroup.add(body);

        // Create zombie head
        const headGeometry = new THREE.SphereGeometry(0.2);
        const headMaterial = new THREE.MeshLambertMaterial({ color: zombieData.color });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.8;
        zombieGroup.add(head);

        // Create glowing red eyes
        const eyeGeometry = new THREE.SphereGeometry(0.03);
        const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.08, 1.85, 0.15);
        zombieGroup.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.08, 1.85, 0.15);
        zombieGroup.add(rightEye);

        // Create arms
        const armGeometry = new THREE.CapsuleGeometry(0.1, 0.6);
        const armMaterial = new THREE.MeshLambertMaterial({ color: zombieData.color });

        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.4, 1.2, 0);
        leftArm.rotation.z = 0.3;
        zombieGroup.add(leftArm);

        const rightArm = new THREE.Mesh(armGeometry, armMaterial);
        rightArm.position.set(0.4, 1.2, 0);
        rightArm.rotation.z = -0.3;
        zombieGroup.add(rightArm);

        // Create legs
        const legGeometry = new THREE.CapsuleGeometry(0.12, 0.8);
        const legMaterial = new THREE.MeshLambertMaterial({ color: zombieData.color });

        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.15, 0.4, 0);
        zombieGroup.add(leftLeg);

        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.15, 0.4, 0);
        zombieGroup.add(rightLeg);

        // Set position and scale
        zombieGroup.position.copy(position);
        zombieGroup.scale.setScalar(zombieData.scale);

        // Add zombie properties
        zombieGroup.userData = {
            type: type,
            health: zombieData.health,
            maxHealth: zombieData.health,
            speed: zombieData.speed,
            damage: zombieData.damage,
            attackRange: zombieData.attackRange,
            state: 'idle', // idle, chasing, attacking, dead
            target: null,
            lastAttack: 0,
            attackCooldown: 1000,
            walkAnimation: 0,
            deathTime: 0
        };

        // Add shadow
        zombieGroup.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });

        this.scene.add(zombieGroup);
        this.zombies.push(zombieGroup);

        return zombieGroup;
    }

    spawnZombie() {
        if (this.zombies.length >= this.maxZombies) return;

        // Choose random spawn point
        const spawnPoint = this.zombieSpawnPoints[Math.floor(Math.random() * this.zombieSpawnPoints.length)];

        // Choose random zombie type
        const types = ['walker', 'walker', 'walker', 'runner', 'brute']; // Weighted towards walkers
        const type = types[Math.floor(Math.random() * types.length)];

        // Create zombie at spawn point
        const zombie = this.createZombie(spawnPoint.clone(), type);

        console.log(`Spawned ${type} zombie at`, spawnPoint);
    }
    
    async initAudio() {
        // Audio will be initialized after first user interaction
        this.sounds = {
            gunshot: null,
            reload: null,
            zombieGrowl: null,
            ambient: null,
            empty: null,
            weaponSwitch: null,
            zombieAttack: null,
            zombieDeath: null,
            gameOver: null
        };

        // Audio settings
        this.audioSettings = {
            masterVolume: 0.7,
            sfxVolume: 0.8,
            musicVolume: 0.5
        };
    }

    async createWeaponModels() {
        // Create simple weapon models using basic geometries
        this.createPistolModel();
        this.createRifleModel();
        this.createShotgunModel();

        // Create muzzle flash effect
        this.createMuzzleFlashEffect();

        // Set initial weapon
        this.switchWeapon(this.currentWeapon);
    }

    createPistolModel() {
        const pistolGroup = new THREE.Group();

        // Barrel
        const barrelGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0.15, 0, 0);
        pistolGroup.add(barrel);

        // Handle
        const handleGeometry = new THREE.BoxGeometry(0.05, 0.2, 0.03);
        const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const handle = new THREE.Mesh(handleGeometry, handleMaterial);
        handle.position.set(-0.05, -0.1, 0);
        pistolGroup.add(handle);

        // Body
        const bodyGeometry = new THREE.BoxGeometry(0.15, 0.08, 0.03);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.set(0, 0, 0);
        pistolGroup.add(body);

        // Position weapon in front of camera
        pistolGroup.position.set(0.3, -0.3, -0.5);
        pistolGroup.rotation.y = -0.1;

        this.weapons.pistol.model = pistolGroup;
    }

    createRifleModel() {
        const rifleGroup = new THREE.Group();

        // Barrel
        const barrelGeometry = new THREE.CylinderGeometry(0.025, 0.025, 0.6);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0.3, 0, 0);
        rifleGroup.add(barrel);

        // Stock
        const stockGeometry = new THREE.BoxGeometry(0.3, 0.05, 0.03);
        const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const stock = new THREE.Mesh(stockGeometry, stockMaterial);
        stock.position.set(-0.15, 0, 0);
        rifleGroup.add(stock);

        // Body
        const bodyGeometry = new THREE.BoxGeometry(0.4, 0.08, 0.05);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.set(0, 0, 0);
        rifleGroup.add(body);

        // Scope
        const scopeGeometry = new THREE.CylinderGeometry(0.015, 0.015, 0.2);
        const scopeMaterial = new THREE.MeshLambertMaterial({ color: 0x111111 });
        const scope = new THREE.Mesh(scopeGeometry, scopeMaterial);
        scope.rotation.z = Math.PI / 2;
        scope.position.set(0.1, 0.05, 0);
        rifleGroup.add(scope);

        // Position weapon
        rifleGroup.position.set(0.2, -0.2, -0.8);
        rifleGroup.rotation.y = -0.05;

        this.weapons.rifle.model = rifleGroup;
    }

    createShotgunModel() {
        const shotgunGroup = new THREE.Group();

        // Barrel (wider than rifle)
        const barrelGeometry = new THREE.CylinderGeometry(0.04, 0.04, 0.5);
        const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
        barrel.rotation.z = Math.PI / 2;
        barrel.position.set(0.25, 0, 0);
        shotgunGroup.add(barrel);

        // Stock
        const stockGeometry = new THREE.BoxGeometry(0.25, 0.06, 0.04);
        const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const stock = new THREE.Mesh(stockGeometry, stockMaterial);
        stock.position.set(-0.125, 0, 0);
        shotgunGroup.add(stock);

        // Body
        const bodyGeometry = new THREE.BoxGeometry(0.3, 0.1, 0.06);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.set(0, 0, 0);
        shotgunGroup.add(body);

        // Pump
        const pumpGeometry = new THREE.BoxGeometry(0.08, 0.03, 0.04);
        const pumpMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const pump = new THREE.Mesh(pumpGeometry, pumpMaterial);
        pump.position.set(0.1, -0.06, 0);
        shotgunGroup.add(pump);

        // Position weapon
        shotgunGroup.position.set(0.2, -0.25, -0.7);
        shotgunGroup.rotation.y = -0.05;

        this.weapons.shotgun.model = shotgunGroup;
    }

    createMuzzleFlashEffect() {
        const flashGeometry = new THREE.ConeGeometry(0.05, 0.2, 8);
        const flashMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.muzzleFlash = new THREE.Mesh(flashGeometry, flashMaterial);
        this.muzzleFlash.rotation.z = -Math.PI / 2;
        this.muzzleFlash.visible = false;
    }
    
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // Mouse events
        document.addEventListener('mousedown', (event) => this.onMouseDown(event));
        document.addEventListener('mouseup', (event) => this.onMouseUp(event));
        
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // UI events
        document.getElementById('startButton').addEventListener('click', () => this.startGame());
        document.getElementById('restartButton').addEventListener('click', () => this.restartGame());
        document.getElementById('resumeButton').addEventListener('click', () => this.resumeGame());
        document.getElementById('mainMenuButton').addEventListener('click', () => this.returnToMainMenu());

        // Mobile controls
        if (this.isMobile()) {
            this.setupMobileControls();
        }
        
        // Pointer lock events
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
    }
    
    onKeyDown(event) {
        this.keys[event.code] = true;
        
        // Weapon switching
        if (event.code === 'Digit1') this.switchWeapon('pistol');
        if (event.code === 'Digit2') this.switchWeapon('rifle');
        if (event.code === 'Digit3') this.switchWeapon('shotgun');
        
        // Reload
        if (event.code === 'KeyR') this.reload();
        
        // Pause
        if (event.code === 'Escape' && this.gameState === 'playing') {
            this.pauseGame();
        }
    }
    
    onKeyUp(event) {
        this.keys[event.code] = false;
    }
    
    onMouseDown(event) {
        if (event.button === 0) { // Left click
            this.mousePressed = true;
            if (this.gameState === 'playing') {
                this.shoot();
            }
        }
    }
    
    onMouseUp(event) {
        if (event.button === 0) {
            this.mousePressed = false;
        }
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onPointerLockChange() {
        if (document.pointerLockElement === document.body) {
            this.controls.enabled = true;
        } else {
            this.controls.enabled = false;
            if (this.gameState === 'playing') {
                this.pauseGame();
            }
        }
    }
    
    // UI Methods
    showLoadingProgress(percent) {
        const progressBar = document.getElementById('loadingProgress');
        if (progressBar) {
            progressBar.style.width = percent + '%';
        }
    }
    
    hideLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
    
    showStartScreen() {
        const startScreen = document.getElementById('startScreen');
        if (startScreen) {
            startScreen.style.display = 'flex';
        }
        this.gameState = 'menu';
    }
    
    hideStartScreen() {
        const startScreen = document.getElementById('startScreen');
        if (startScreen) {
            startScreen.style.display = 'none';
        }
    }
    
    startGame() {
        this.hideStartScreen();
        this.gameState = 'playing';
        
        // Request pointer lock
        document.body.requestPointerLock();
        
        // Initialize audio context after user interaction
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            // Start ambient audio
            setTimeout(() => this.startAmbientAudio(), 1000);
        }

        // Start game loop
        this.gameLoop();
    }
    
    // Game loop and update methods will be added in the next section
    gameLoop() {
        if (this.gameState !== 'playing') return;
        
        this.deltaTime = this.clock.getDelta();
        
        this.updatePlayer();
        this.updateGame();
        this.render();
        
        requestAnimationFrame(() => this.gameLoop());
    }
    
    updatePlayer() {
        if (!this.controls.enabled) return;

        const moveVector = new THREE.Vector3();
        const direction = new THREE.Vector3();

        // Get movement input
        if (this.keys['KeyW']) moveVector.z -= 1;
        if (this.keys['KeyS']) moveVector.z += 1;
        if (this.keys['KeyA']) moveVector.x -= 1;
        if (this.keys['KeyD']) moveVector.x += 1;

        // Normalize movement vector
        if (moveVector.length() > 0) {
            moveVector.normalize();
        }

        // Apply camera direction to movement
        this.camera.getWorldDirection(direction);
        direction.y = 0;
        direction.normalize();

        const right = new THREE.Vector3();
        right.crossVectors(direction, this.camera.up).normalize();

        // Calculate final movement
        const finalMovement = new THREE.Vector3();
        finalMovement.addScaledVector(direction, -moveVector.z);
        finalMovement.addScaledVector(right, moveVector.x);

        // Apply movement
        const moveSpeed = this.player.speed * this.deltaTime;
        this.player.velocity.x = finalMovement.x * moveSpeed;
        this.player.velocity.z = finalMovement.z * moveSpeed;

        // Apply gravity
        this.player.velocity.y -= 25 * this.deltaTime;

        // Jump
        if (this.keys['Space'] && this.player.onGround) {
            this.player.velocity.y = this.player.jumpPower;
            this.player.onGround = false;
        }

        // Update position
        this.player.position.add(this.player.velocity.clone().multiplyScalar(this.deltaTime));

        // Ground collision
        if (this.player.position.y <= this.player.height) {
            this.player.position.y = this.player.height;
            this.player.velocity.y = 0;
            this.player.onGround = true;
        }

        // Building collision (simple box collision)
        this.checkBuildingCollisions();

        // Update camera position
        this.controls.getObject().position.copy(this.player.position);
    }

    checkBuildingCollisions() {
        const playerBox = new THREE.Box3().setFromCenterAndSize(
            this.player.position,
            new THREE.Vector3(1, this.player.height, 1)
        );

        for (const building of this.buildings) {
            const buildingBox = new THREE.Box3().setFromObject(building);

            if (playerBox.intersectsBox(buildingBox)) {
                // Simple collision response - push player away
                const buildingCenter = buildingBox.getCenter(new THREE.Vector3());
                const direction = this.player.position.clone().sub(buildingCenter);
                direction.y = 0;
                direction.normalize();

                // Push player outside building
                const distance = 2; // Safe distance
                this.player.position.x = buildingCenter.x + direction.x * (buildingBox.getSize(new THREE.Vector3()).x / 2 + distance);
                this.player.position.z = buildingCenter.z + direction.z * (buildingBox.getSize(new THREE.Vector3()).z / 2 + distance);
            }
        }
    }

    updateGame() {
        // Update UI
        this.updateUI();

        // Update bullets
        this.updateBullets();

        // Update zombies (will be implemented later)
        this.updateZombies();

        // Update effects
        this.updateEffects();

        // Update particle systems
        this.updateParticles();
    }

    updateUI() {
        // Update health bar
        const healthFill = document.getElementById('healthFill');
        if (healthFill) {
            const healthPercent = (this.health / this.maxHealth) * 100;
            healthFill.style.width = healthPercent + '%';
        }

        // Update ammo counter
        const ammoCounter = document.getElementById('ammoCounter');
        if (ammoCounter) {
            const weapon = this.weapons[this.currentWeapon];
            ammoCounter.textContent = `Ammo: ${weapon.ammo}/${weapon.maxAmmo}`;
        }

        // Update weapon info
        const weaponInfo = document.getElementById('weaponInfo');
        if (weaponInfo) {
            const weaponNames = {
                pistol: 'Pistol',
                rifle: 'Assault Rifle',
                shotgun: 'Shotgun'
            };
            weaponInfo.textContent = weaponNames[this.currentWeapon];
        }

        // Update score
        const scoreDisplay = document.getElementById('scoreDisplay');
        if (scoreDisplay) {
            scoreDisplay.textContent = `Score: ${this.score}`;
        }
    }

    updateBullets() {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            // Move bullet
            bullet.position.add(bullet.velocity.clone().multiplyScalar(this.deltaTime));

            // Check if bullet is too far or hit something
            if (bullet.distanceTraveled > bullet.maxDistance) {
                this.scene.remove(bullet);
                this.bullets.splice(i, 1);
                continue;
            }

            bullet.distanceTraveled += bullet.velocity.length() * this.deltaTime;

            // Check collision with zombies
            if (this.checkBulletZombieCollision(bullet, i)) continue;

            // Check collision with buildings
            this.checkBulletBuildingCollision(bullet, i);
        }
    }

    checkBulletZombieCollision(bullet, bulletIndex) {
        const bulletBox = new THREE.Box3().setFromCenterAndSize(
            bullet.position,
            new THREE.Vector3(0.1, 0.1, 0.1)
        );

        for (let i = 0; i < this.zombies.length; i++) {
            const zombie = this.zombies[i];

            // Skip dead zombies
            if (zombie.userData.state === 'dead') continue;

            const zombieBox = new THREE.Box3().setFromObject(zombie);

            if (bulletBox.intersectsBox(zombieBox)) {
                // Remove bullet
                this.scene.remove(bullet);
                this.bullets.splice(bulletIndex, 1);

                // Damage zombie
                this.damageZombie(zombie, bullet.damage);

                // Show hit indicator
                this.showHitIndicator();

                // Create blood effect
                this.createBloodEffect(bullet.position);

                // Create impact effect
                this.createImpactEffect(bullet.position);

                return true; // Bullet hit something
            }
        }

        return false; // Bullet didn't hit anything
    }

    checkBulletBuildingCollision(bullet, bulletIndex) {
        const bulletBox = new THREE.Box3().setFromCenterAndSize(
            bullet.position,
            new THREE.Vector3(0.1, 0.1, 0.1)
        );

        for (const building of this.buildings) {
            const buildingBox = new THREE.Box3().setFromObject(building);

            if (bulletBox.intersectsBox(buildingBox)) {
                // Remove bullet
                this.scene.remove(bullet);
                this.bullets.splice(bulletIndex, 1);

                // Create impact effect
                this.createImpactEffect(bullet.position);
                break;
            }
        }
    }

    damageZombie(zombie, damage) {
        zombie.userData.health -= damage;

        if (zombie.userData.health <= 0) {
            this.killZombie(zombie);
        } else {
            // Zombie hit reaction
            this.animateZombieHit(zombie);
        }
    }

    killZombie(zombie) {
        zombie.userData.state = 'dead';
        zombie.userData.deathTime = Date.now();

        // Death animation - fall over
        zombie.rotation.z = Math.PI / 2;
        zombie.position.y = 0.5;

        // Change color to indicate death
        zombie.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material.color.multiplyScalar(0.5);
            }
        });

        // Add score
        const points = {
            walker: 10,
            runner: 15,
            brute: 25
        };
        this.score += points[zombie.userData.type] || 10;

        // Show kill feed
        this.showKillFeed(zombie.userData.type);

        // Play death sound
        this.playSound('zombieDeath');
    }

    animateZombieHit(zombie) {
        // Brief red flash
        zombie.traverse((child) => {
            if (child.isMesh && child.material) {
                const originalColor = child.material.color.clone();
                child.material.color.setHex(0xff0000);

                setTimeout(() => {
                    child.material.color.copy(originalColor);
                }, 100);
            }
        });

        // Slight knockback
        const direction = new THREE.Vector3();
        direction.subVectors(zombie.position, this.player.position);
        direction.y = 0;
        direction.normalize();
        zombie.position.add(direction.multiplyScalar(0.2));
    }

    createBloodEffect(position) {
        // Create blood splatter particles
        const bloodCount = 8;
        for (let i = 0; i < bloodCount; i++) {
            const bloodGeometry = new THREE.SphereGeometry(0.02);
            const bloodMaterial = new THREE.MeshBasicMaterial({
                color: 0x660000,
                transparent: true,
                opacity: 0.8
            });
            const blood = new THREE.Mesh(bloodGeometry, bloodMaterial);

            blood.position.copy(position);
            blood.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 3,
                Math.random() * 2 + 1,
                (Math.random() - 0.5) * 3
            );

            this.scene.add(blood);
            this.effects.push(blood);

            // Animate blood falling
            const animateBlood = () => {
                blood.velocity.y -= 9.8 * this.deltaTime; // Gravity
                blood.position.add(blood.velocity.clone().multiplyScalar(this.deltaTime));

                if (blood.position.y <= 0.1) {
                    // Blood hit ground, create splatter
                    blood.geometry = new THREE.PlaneGeometry(0.2, 0.2);
                    blood.rotation.x = -Math.PI / 2;
                    blood.position.y = 0.01;

                    // Remove after time
                    setTimeout(() => {
                        this.scene.remove(blood);
                        const index = this.effects.indexOf(blood);
                        if (index > -1) this.effects.splice(index, 1);
                    }, 10000);
                } else {
                    requestAnimationFrame(animateBlood);
                }
            };

            animateBlood();
        }
    }

    updateZombies() {
        // Spawn new zombies
        const currentTime = Date.now();
        if (currentTime - this.lastZombieSpawn > this.zombieSpawnRate) {
            this.spawnZombie();
            this.lastZombieSpawn = currentTime;
        }

        // Update each zombie
        for (let i = this.zombies.length - 1; i >= 0; i--) {
            const zombie = this.zombies[i];
            this.updateZombieAI(zombie);

            // Remove dead zombies
            if (zombie.userData.state === 'dead' &&
                currentTime - zombie.userData.deathTime > 5000) {
                this.scene.remove(zombie);
                this.zombies.splice(i, 1);
            }
        }
    }

    updateZombieAI(zombie) {
        const userData = zombie.userData;
        const playerPosition = this.player.position;
        const zombiePosition = zombie.position;

        // Skip if zombie is dead
        if (userData.state === 'dead') return;

        // Calculate distance to player
        const distanceToPlayer = zombiePosition.distanceTo(playerPosition);

        // State machine
        switch (userData.state) {
            case 'idle':
                // Look for player
                if (distanceToPlayer < 30) {
                    userData.state = 'chasing';
                    userData.target = playerPosition.clone();
                }
                break;

            case 'chasing':
                // Chase player
                if (distanceToPlayer < userData.attackRange) {
                    userData.state = 'attacking';
                } else if (distanceToPlayer > 50) {
                    userData.state = 'idle';
                } else {
                    this.moveZombieTowardsTarget(zombie, playerPosition);
                }
                break;

            case 'attacking':
                // Attack player
                if (distanceToPlayer > userData.attackRange) {
                    userData.state = 'chasing';
                } else {
                    this.zombieAttackPlayer(zombie);
                }
                break;
        }

        // Animate zombie
        this.animateZombie(zombie);
    }

    moveZombieTowardsTarget(zombie, target) {
        const direction = new THREE.Vector3();
        direction.subVectors(target, zombie.position);
        direction.y = 0; // Keep on ground
        direction.normalize();

        // Move zombie
        const moveSpeed = zombie.userData.speed * this.deltaTime;
        zombie.position.add(direction.multiplyScalar(moveSpeed));

        // Face target
        zombie.lookAt(target.x, zombie.position.y, target.z);

        // Simple collision with buildings
        this.checkZombieBuildingCollision(zombie);
    }

    checkZombieBuildingCollision(zombie) {
        const zombieBox = new THREE.Box3().setFromObject(zombie);

        for (const building of this.buildings) {
            const buildingBox = new THREE.Box3().setFromObject(building);

            if (zombieBox.intersectsBox(buildingBox)) {
                // Push zombie away from building
                const buildingCenter = buildingBox.getCenter(new THREE.Vector3());
                const direction = zombie.position.clone().sub(buildingCenter);
                direction.y = 0;
                direction.normalize();

                zombie.position.add(direction.multiplyScalar(0.5));
                break;
            }
        }
    }

    zombieAttackPlayer(zombie) {
        const currentTime = Date.now();
        const userData = zombie.userData;

        if (currentTime - userData.lastAttack > userData.attackCooldown) {
            // Deal damage to player
            this.takeDamage(userData.damage);
            userData.lastAttack = currentTime;

            // Play attack animation
            this.animateZombieAttack(zombie);

            // Play zombie attack sound
            this.playSound('zombieAttack');
        }
    }

    animateZombie(zombie) {
        const userData = zombie.userData;

        if (userData.state === 'chasing') {
            // Walking animation
            userData.walkAnimation += this.deltaTime * 5;

            // Bob up and down
            zombie.position.y = Math.sin(userData.walkAnimation) * 0.1;

            // Sway arms
            const arms = zombie.children.filter(child =>
                child.position.x !== 0 && child.position.y > 1
            );
            arms.forEach((arm, index) => {
                arm.rotation.x = Math.sin(userData.walkAnimation + index * Math.PI) * 0.3;
            });
        }
    }

    animateZombieAttack(zombie) {
        // Quick attack animation
        const arms = zombie.children.filter(child =>
            child.position.x !== 0 && child.position.y > 1
        );

        arms.forEach(arm => {
            arm.rotation.x = -0.5; // Swing forward
            setTimeout(() => {
                arm.rotation.x = 0.3; // Return
            }, 200);
        });
    }

    takeDamage(amount) {
        this.health -= amount;
        if (this.health <= 0) {
            this.health = 0;
            this.gameOver();
        }

        // Flash screen red for damage indicator
        this.flashDamageIndicator();
    }

    flashDamageIndicator() {
        // Create red overlay
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
        overlay.style.pointerEvents = 'none';
        overlay.style.zIndex = '150';
        document.body.appendChild(overlay);

        // Remove after short time
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 200);
    }

    updateEffects() {
        // Animate dust particles
        if (this.dustParticles) {
            this.dustParticles.rotation.y += 0.001;
            const positions = this.dustParticles.geometry.attributes.position.array;
            for (let i = 1; i < positions.length; i += 3) {
                positions[i] += 0.01; // Slow upward movement
                if (positions[i] > 50) positions[i] = 0; // Reset when too high
            }
            this.dustParticles.geometry.attributes.position.needsUpdate = true;
        }

        // Animate fire effects
        if (this.fireEffects) {
            this.fireEffects.forEach(effect => {
                // Flicker fire
                effect.fire.scale.y = 1 + Math.sin(Date.now() * 0.01) * 0.2;
                effect.light.intensity = 0.8 + Math.sin(Date.now() * 0.015) * 0.3;
            });
        }

        // Flicker street lights
        if (this.streetLights) {
            this.streetLights.forEach(light => {
                if (Math.random() < 0.02) { // 2% chance per frame
                    light.intensity = light.intensity > 0 ? 0 : 0.5;
                }
            });
        }

        // Blink emergency lights
        if (this.emergencyLights) {
            const time = Date.now() * 0.003;
            this.emergencyLights.forEach((light, index) => {
                light.intensity = Math.sin(time + index) > 0 ? 0.8 : 0.1;
            });
        }
    }

    updateParticles() {
        // Update all active particles
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            // Update particle physics
            if (particle.velocity) {
                particle.velocity.y -= 9.8 * this.deltaTime; // Gravity
                particle.position.add(particle.velocity.clone().multiplyScalar(this.deltaTime));
            }

            // Update particle life
            particle.life -= this.deltaTime;

            // Fade out particle
            if (particle.material && particle.material.opacity !== undefined) {
                particle.material.opacity = Math.max(0, particle.life / particle.maxLife);
            }

            // Remove dead particles
            if (particle.life <= 0) {
                this.scene.remove(particle);
                this.particles.splice(i, 1);
            }
        }
    }

    createExplosionEffect(position, size = 1) {
        // Create explosion particles
        const particleCount = 20;

        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.05 * size);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(0.1, 1, 0.5 + Math.random() * 0.5),
                transparent: true,
                opacity: 1
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            particle.position.copy(position);
            particle.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 10 * size,
                Math.random() * 8 * size,
                (Math.random() - 0.5) * 10 * size
            );

            particle.life = 1 + Math.random();
            particle.maxLife = particle.life;

            this.scene.add(particle);
            this.particles.push(particle);
        }

        // Create explosion flash
        const flashGeometry = new THREE.SphereGeometry(2 * size);
        const flashMaterial = new THREE.MeshBasicMaterial({
            color: 0xffaa00,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });
        const flash = new THREE.Mesh(flashGeometry, flashMaterial);
        flash.position.copy(position);

        this.scene.add(flash);

        // Remove flash quickly
        setTimeout(() => {
            this.scene.remove(flash);
        }, 100);
    }

    createSmokeEffect(position, duration = 3) {
        const smokeCount = 5;

        for (let i = 0; i < smokeCount; i++) {
            const smokeGeometry = new THREE.PlaneGeometry(1, 1);
            const smokeMaterial = new THREE.MeshBasicMaterial({
                color: 0x666666,
                transparent: true,
                opacity: 0.3,
                blending: THREE.NormalBlending
            });
            const smoke = new THREE.Mesh(smokeGeometry, smokeMaterial);

            smoke.position.copy(position);
            smoke.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                Math.random() * 2,
                (Math.random() - 0.5) * 2
            ));

            smoke.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 0.5,
                1 + Math.random() * 0.5,
                (Math.random() - 0.5) * 0.5
            );

            smoke.life = duration;
            smoke.maxLife = duration;

            // Make smoke face camera
            smoke.lookAt(this.camera.position);

            this.scene.add(smoke);
            this.particles.push(smoke);
        }
    }

    createShellCasing(position, direction) {
        // Create bullet shell casing
        const shellGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.05);
        const shellMaterial = new THREE.MeshLambertMaterial({ color: 0xffaa00 });
        const shell = new THREE.Mesh(shellGeometry, shellMaterial);

        shell.position.copy(position);
        shell.position.add(new THREE.Vector3(0.2, -0.1, 0)); // Offset from weapon

        // Eject shell to the right
        const rightVector = new THREE.Vector3();
        rightVector.crossVectors(direction, this.camera.up);

        shell.velocity = new THREE.Vector3();
        shell.velocity.copy(rightVector.multiplyScalar(3));
        shell.velocity.y = 2; // Upward component
        shell.velocity.add(direction.multiplyScalar(-1)); // Backward component

        shell.angularVelocity = new THREE.Vector3(
            Math.random() * 10,
            Math.random() * 10,
            Math.random() * 10
        );

        shell.life = 5; // Shell stays for 5 seconds
        shell.maxLife = 5;
        shell.bounced = false;

        this.scene.add(shell);
        this.particles.push(shell);

        // Add shell physics
        const updateShell = () => {
            if (shell.life <= 0) return;

            // Apply gravity
            shell.velocity.y -= 9.8 * this.deltaTime;

            // Update position
            shell.position.add(shell.velocity.clone().multiplyScalar(this.deltaTime));

            // Update rotation
            shell.rotation.x += shell.angularVelocity.x * this.deltaTime;
            shell.rotation.y += shell.angularVelocity.y * this.deltaTime;
            shell.rotation.z += shell.angularVelocity.z * this.deltaTime;

            // Bounce on ground
            if (shell.position.y <= 0.1 && !shell.bounced) {
                shell.velocity.y = Math.abs(shell.velocity.y) * 0.3; // Bounce with energy loss
                shell.velocity.x *= 0.7;
                shell.velocity.z *= 0.7;
                shell.angularVelocity.multiplyScalar(0.5);
                shell.bounced = true;
            }

            requestAnimationFrame(updateShell);
        };

        updateShell();
    }

    // Weapon system methods
    shoot() {
        const weapon = this.weapons[this.currentWeapon];
        const currentTime = Date.now();

        // Check fire rate
        if (currentTime - this.lastShotTime < weapon.fireRate) return;

        // Check ammo
        if (weapon.ammo <= 0) {
            // Play empty click sound
            this.playSound('empty');
            return;
        }

        // Check if reloading
        if (this.reloading) return;

        // Consume ammo
        weapon.ammo--;
        this.lastShotTime = currentTime;

        // Create bullets (multiple for shotgun)
        for (let i = 0; i < weapon.bulletsPerShot; i++) {
            this.createBullet(weapon);
        }

        // Play gunshot sound
        this.playSound('gunshot');

        // Create muzzle flash effect
        this.createMuzzleFlash();

        // Create shell casing
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        this.createShellCasing(this.camera.position, direction);

        // Weapon recoil animation
        this.animateWeaponRecoil();
    }

    createBullet(weapon) {
        const bulletGeometry = new THREE.SphereGeometry(0.02, 6, 6);
        const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xffaa00 });
        const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

        // Set bullet position to camera position with slight offset
        bullet.position.copy(this.camera.position);
        bullet.position.add(new THREE.Vector3(0.1, -0.1, 0));

        // Set bullet direction with spread
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);

        // Add weapon spread
        const spread = weapon.spread;
        direction.x += (Math.random() - 0.5) * spread;
        direction.y += (Math.random() - 0.5) * spread;
        direction.z += (Math.random() - 0.5) * spread;
        direction.normalize();

        bullet.velocity = direction.multiplyScalar(150); // Bullet speed
        bullet.maxDistance = weapon.range;
        bullet.distanceTraveled = 0;
        bullet.damage = weapon.damage;

        this.scene.add(bullet);
        this.bullets.push(bullet);
    }

    createMuzzleFlash() {
        if (!this.muzzleFlash || !this.weaponModel) return;

        // Position muzzle flash at weapon barrel
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.model) {
            this.muzzleFlash.position.copy(weapon.model.position);
            this.muzzleFlash.position.x += 0.4; // At barrel end
            this.muzzleFlash.rotation.copy(weapon.model.rotation);

            // Add to scene temporarily
            this.scene.add(this.muzzleFlash);
            this.muzzleFlash.visible = true;

            // Random scale for variety
            this.muzzleFlash.scale.setScalar(0.5 + Math.random() * 0.5);

            // Remove after short time
            setTimeout(() => {
                this.muzzleFlash.visible = false;
                this.scene.remove(this.muzzleFlash);
            }, 50);
        }
    }

    animateWeaponRecoil() {
        if (!this.weaponModel) return;

        const originalPosition = this.weaponModel.position.clone();
        const recoilAmount = 0.05;

        // Quick backward movement
        this.weaponModel.position.z += recoilAmount;
        this.weaponModel.rotation.x += 0.1;

        // Return to original position
        setTimeout(() => {
            this.weaponModel.position.copy(originalPosition);
            this.weaponModel.rotation.x -= 0.1;
        }, 100);
    }

    createImpactEffect(position) {
        // Create spark particles
        const sparkCount = 5;
        for (let i = 0; i < sparkCount; i++) {
            const sparkGeometry = new THREE.SphereGeometry(0.01);
            const sparkMaterial = new THREE.MeshBasicMaterial({
                color: 0xffaa00,
                transparent: true,
                opacity: 0.8
            });
            const spark = new THREE.Mesh(sparkGeometry, sparkMaterial);

            spark.position.copy(position);
            spark.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                Math.random() * 2,
                (Math.random() - 0.5) * 2
            );

            this.scene.add(spark);
            this.effects.push(spark);

            // Remove after short time
            setTimeout(() => {
                this.scene.remove(spark);
                const index = this.effects.indexOf(spark);
                if (index > -1) this.effects.splice(index, 1);
            }, 500);
        }
    }

    switchWeapon(weaponName) {
        if (!this.weapons[weaponName] || weaponName === this.currentWeapon) return;

        // Hide current weapon
        if (this.weaponModel) {
            this.camera.remove(this.weaponModel);
        }

        // Show new weapon
        this.currentWeapon = weaponName;
        this.weaponModel = this.weapons[weaponName].model;

        if (this.weaponModel) {
            this.camera.add(this.weaponModel);
        }

        // Play weapon switch sound
        this.playSound('weaponSwitch');

        // Update UI
        this.updateWeaponUI();
    }

    updateWeaponUI() {
        const weaponInfo = document.getElementById('weaponInfo');
        if (weaponInfo) {
            weaponInfo.textContent = this.weapons[this.currentWeapon].name;
        }
    }

    reload() {
        const weapon = this.weapons[this.currentWeapon];

        if (weapon.ammo >= weapon.maxAmmo || this.reloading) return;

        this.reloading = true;

        // Play reload sound
        this.playSound('reload');

        // Show reload animation
        this.animateReload();

        // Reload after delay
        setTimeout(() => {
            weapon.ammo = weapon.maxAmmo;
            this.reloading = false;
        }, weapon.reloadTime);
    }

    animateReload() {
        if (!this.weaponModel) return;

        const originalRotation = this.weaponModel.rotation.clone();

        // Tilt weapon down for reload
        this.weaponModel.rotation.x += 0.3;

        // Return to original position after reload
        setTimeout(() => {
            if (this.weaponModel) {
                this.weaponModel.rotation.copy(originalRotation);
            }
        }, this.weapons[this.currentWeapon].reloadTime);
    }

    playSound(soundName) {
        if (!this.audioContext) return;

        try {
            switch (soundName) {
                case 'gunshot':
                    this.playGunshotSound();
                    break;
                case 'reload':
                    this.playReloadSound();
                    break;
                case 'empty':
                    this.playEmptyClickSound();
                    break;
                case 'weaponSwitch':
                    this.playWeaponSwitchSound();
                    break;
                case 'zombieGrowl':
                    this.playZombieGrowlSound();
                    break;
                case 'zombieAttack':
                    this.playZombieAttackSound();
                    break;
                case 'zombieDeath':
                    this.playZombieDeathSound();
                    break;
                case 'gameOver':
                    this.playGameOverSound();
                    break;
                default:
                    console.log(`Unknown sound: ${soundName}`);
            }
        } catch (error) {
            console.warn('Audio playback error:', error);
        }
    }

    playGunshotSound() {
        const now = this.audioContext.currentTime;

        // Create different gunshot sounds based on weapon
        const weapon = this.currentWeapon;
        let frequency, duration, volume;

        switch (weapon) {
            case 'pistol':
                frequency = 150;
                duration = 0.1;
                volume = 0.6;
                break;
            case 'rifle':
                frequency = 120;
                duration = 0.15;
                volume = 0.8;
                break;
            case 'shotgun':
                frequency = 80;
                duration = 0.3;
                volume = 1.0;
                break;
            default:
                frequency = 120;
                duration = 0.15;
                volume = 0.8;
        }

        // Create noise for gunshot
        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        // Generate noise with envelope
        for (let i = 0; i < bufferSize; i++) {
            const envelope = Math.exp(-i / (bufferSize * 0.1)); // Quick decay
            data[i] = (Math.random() * 2 - 1) * envelope;
        }

        // Create and configure audio nodes
        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        source.buffer = buffer;
        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(frequency, now);

        gainNode.gain.setValueAtTime(volume * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + duration);

        // Connect nodes
        source.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Play sound
        source.start(now);
        source.stop(now + duration);
    }

    playReloadSound() {
        const now = this.audioContext.currentTime;

        // Create reload sound (metallic clicks)
        const clickCount = 3;
        const clickInterval = 0.2;

        for (let i = 0; i < clickCount; i++) {
            const clickTime = now + i * clickInterval;

            // Create click sound
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(800 + i * 200, clickTime);

            gainNode.gain.setValueAtTime(0, clickTime);
            gainNode.gain.linearRampToValueAtTime(0.3 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, clickTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, clickTime + 0.1);

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.start(clickTime);
            oscillator.stop(clickTime + 0.1);
        }
    }

    playEmptyClickSound() {
        const now = this.audioContext.currentTime;

        // Create empty click sound
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(1000, now);

        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(0.2 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + 0.05);

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.start(now);
        oscillator.stop(now + 0.05);
    }

    playWeaponSwitchSound() {
        const now = this.audioContext.currentTime;

        // Create weapon switch sound (metallic slide)
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(400, now);
        oscillator.frequency.linearRampToValueAtTime(200, now + 0.3);

        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(800, now);

        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(0.4 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now + 0.05);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + 0.3);

        oscillator.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.start(now);
        oscillator.stop(now + 0.3);
    }

    playZombieGrowlSound() {
        const now = this.audioContext.currentTime;

        // Create zombie growl (low frequency noise)
        const bufferSize = this.audioContext.sampleRate * 1.5;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        // Generate low-frequency noise
        for (let i = 0; i < bufferSize; i++) {
            const envelope = Math.sin(i / bufferSize * Math.PI) * 0.5;
            data[i] = (Math.random() * 2 - 1) * envelope;
        }

        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        source.buffer = buffer;
        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(200, now);

        gainNode.gain.setValueAtTime(0.3 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + 1.5);

        source.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        source.start(now);
        source.stop(now + 1.5);
    }

    playZombieAttackSound() {
        const now = this.audioContext.currentTime;

        // Create zombie attack sound (aggressive growl)
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(80, now);
        oscillator.frequency.linearRampToValueAtTime(120, now + 0.2);
        oscillator.frequency.linearRampToValueAtTime(60, now + 0.5);

        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(300, now);

        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(0.5 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + 0.5);

        oscillator.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.start(now);
        oscillator.stop(now + 0.5);
    }

    playZombieDeathSound() {
        const now = this.audioContext.currentTime;

        // Create zombie death sound (dying growl)
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(150, now);
        oscillator.frequency.exponentialRampToValueAtTime(30, now + 2);

        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(400, now);
        filterNode.frequency.exponentialRampToValueAtTime(100, now + 2);

        gainNode.gain.setValueAtTime(0.4 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, now);
        gainNode.gain.exponentialRampToValueAtTime(0.01, now + 2);

        oscillator.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.start(now);
        oscillator.stop(now + 2);
    }

    playGameOverSound() {
        const now = this.audioContext.currentTime;

        // Create dramatic game over sound
        const frequencies = [220, 185, 165, 147]; // Descending notes

        frequencies.forEach((freq, index) => {
            const noteTime = now + index * 0.5;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(freq, noteTime);

            gainNode.gain.setValueAtTime(0, noteTime);
            gainNode.gain.linearRampToValueAtTime(0.3 * this.audioSettings.sfxVolume * this.audioSettings.masterVolume, noteTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.01, noteTime + 1);

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.start(noteTime);
            oscillator.stop(noteTime + 1);
        });
    }

    startAmbientAudio() {
        if (!this.audioContext || this.ambientAudio) return;

        // Create ambient city sounds
        this.createWindAmbient();
        this.createDistantSounds();
    }

    createWindAmbient() {
        // Create continuous wind sound
        const bufferSize = this.audioContext.sampleRate * 4; // 4 second loop
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        // Generate wind noise
        for (let i = 0; i < bufferSize; i++) {
            data[i] = (Math.random() * 2 - 1) * 0.1;
        }

        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        const filterNode = this.audioContext.createBiquadFilter();

        source.buffer = buffer;
        source.loop = true;

        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(400, this.audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.2 * this.audioSettings.musicVolume * this.audioSettings.masterVolume, this.audioContext.currentTime);

        source.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        source.start();

        this.ambientAudio = { source, gainNode };
    }

    createDistantSounds() {
        // Randomly play distant sounds
        const playDistantSound = () => {
            if (!this.audioContext || this.gameState !== 'playing') return;

            const sounds = ['distantGunshot', 'distantScream', 'distantExplosion'];
            const soundType = sounds[Math.floor(Math.random() * sounds.length)];

            this.playDistantSound(soundType);

            // Schedule next distant sound
            setTimeout(playDistantSound, 10000 + Math.random() * 20000); // 10-30 seconds
        };

        // Start distant sounds after a delay
        setTimeout(playDistantSound, 5000);
    }

    playDistantSound(soundType) {
        const now = this.audioContext.currentTime;

        switch (soundType) {
            case 'distantGunshot':
                // Muffled gunshot
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filterNode = this.audioContext.createBiquadFilter();

                oscillator.type = 'square';
                oscillator.frequency.setValueAtTime(100, now);

                filterNode.type = 'lowpass';
                filterNode.frequency.setValueAtTime(200, now);

                gainNode.gain.setValueAtTime(0, now);
                gainNode.gain.linearRampToValueAtTime(0.1 * this.audioSettings.musicVolume * this.audioSettings.masterVolume, now + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, now + 0.3);

                oscillator.connect(filterNode);
                filterNode.connect(gainNode);
                gainNode.connect(this.audioContext.destination);

                oscillator.start(now);
                oscillator.stop(now + 0.3);
                break;

            case 'distantScream':
                // High-pitched distant scream
                const screamOsc = this.audioContext.createOscillator();
                const screamGain = this.audioContext.createGain();

                screamOsc.type = 'sine';
                screamOsc.frequency.setValueAtTime(800, now);
                screamOsc.frequency.linearRampToValueAtTime(600, now + 1);

                screamGain.gain.setValueAtTime(0, now);
                screamGain.gain.linearRampToValueAtTime(0.05 * this.audioSettings.musicVolume * this.audioSettings.masterVolume, now + 0.2);
                screamGain.gain.exponentialRampToValueAtTime(0.01, now + 1);

                screamOsc.connect(screamGain);
                screamGain.connect(this.audioContext.destination);

                screamOsc.start(now);
                screamOsc.stop(now + 1);
                break;
        }
    }

    pauseGame() {
        this.gameState = 'paused';

        // Release pointer lock
        if (document.pointerLockElement) {
            document.exitPointerLock();
        }

        // Show pause menu
        const pauseScreen = document.getElementById('pauseScreen');
        if (pauseScreen) {
            pauseScreen.style.display = 'flex';
        }
    }

    resumeGame() {
        this.gameState = 'playing';

        // Hide pause menu
        const pauseScreen = document.getElementById('pauseScreen');
        if (pauseScreen) {
            pauseScreen.style.display = 'none';
        }

        // Request pointer lock again
        document.body.requestPointerLock();

        // Resume game loop
        this.gameLoop();
    }

    returnToMainMenu() {
        this.gameState = 'menu';

        // Hide pause menu
        const pauseScreen = document.getElementById('pauseScreen');
        if (pauseScreen) {
            pauseScreen.style.display = 'none';
        }

        // Show start screen
        this.showStartScreen();

        // Reset game
        this.resetGame();
    }

    resetGame() {
        // Clear all game objects
        this.clearBullets();
        this.clearZombies();
        this.clearParticles();

        // Reset player
        this.health = this.maxHealth;
        this.score = 0;
        this.player.position.set(0, 1.8, 0);

        // Reset weapons
        for (const weaponName in this.weapons) {
            this.weapons[weaponName].ammo = this.weapons[weaponName].maxAmmo;
        }

        // Reset UI
        this.updateUI();
    }

    clearParticles() {
        for (const particle of this.particles) {
            this.scene.remove(particle);
        }
        this.particles = [];
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    setupMobileControls() {
        const mobileControls = document.getElementById('mobileControls');
        if (mobileControls) {
            mobileControls.style.display = 'block';
        }

        // Setup virtual joystick
        this.setupVirtualJoystick();

        // Setup mobile buttons
        const fireButton = document.getElementById('fireButton');
        const reloadButton = document.getElementById('reloadButton');

        if (fireButton) {
            fireButton.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.mousePressed = true;
                this.shoot();
            });

            fireButton.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.mousePressed = false;
            });
        }

        if (reloadButton) {
            reloadButton.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.reload();
            });
        }
    }

    setupVirtualJoystick() {
        const joystick = document.getElementById('joystick');
        const joystickKnob = document.getElementById('joystickKnob');

        if (!joystick || !joystickKnob) return;

        let isDragging = false;
        let startPos = { x: 0, y: 0 };
        let currentPos = { x: 0, y: 0 };

        const handleStart = (e) => {
            e.preventDefault();
            isDragging = true;
            const rect = joystick.getBoundingClientRect();
            startPos.x = rect.left + rect.width / 2;
            startPos.y = rect.top + rect.height / 2;
        };

        const handleMove = (e) => {
            if (!isDragging) return;
            e.preventDefault();

            const touch = e.touches ? e.touches[0] : e;
            const deltaX = touch.clientX - startPos.x;
            const deltaY = touch.clientY - startPos.y;

            // Limit movement to joystick radius
            const maxDistance = 30;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance <= maxDistance) {
                currentPos.x = deltaX;
                currentPos.y = deltaY;
            } else {
                currentPos.x = (deltaX / distance) * maxDistance;
                currentPos.y = (deltaY / distance) * maxDistance;
            }

            // Update knob position
            joystickKnob.style.transform = `translate(calc(-50% + ${currentPos.x}px), calc(-50% + ${currentPos.y}px))`;

            // Update movement keys
            this.keys['KeyW'] = currentPos.y < -10;
            this.keys['KeyS'] = currentPos.y > 10;
            this.keys['KeyA'] = currentPos.x < -10;
            this.keys['KeyD'] = currentPos.x > 10;
        };

        const handleEnd = (e) => {
            e.preventDefault();
            isDragging = false;
            currentPos.x = 0;
            currentPos.y = 0;

            // Reset knob position
            joystickKnob.style.transform = 'translate(-50%, -50%)';

            // Clear movement keys
            this.keys['KeyW'] = false;
            this.keys['KeyS'] = false;
            this.keys['KeyA'] = false;
            this.keys['KeyD'] = false;
        };

        // Touch events
        joystick.addEventListener('touchstart', handleStart);
        document.addEventListener('touchmove', handleMove);
        document.addEventListener('touchend', handleEnd);

        // Mouse events for testing
        joystick.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', handleEnd);
    }

    showHitIndicator() {
        const hitIndicator = document.getElementById('hitIndicator');
        if (hitIndicator) {
            hitIndicator.style.opacity = '1';
            setTimeout(() => {
                hitIndicator.style.opacity = '0';
            }, 100);
        }
    }

    showKillFeed(zombieType) {
        const killFeed = document.getElementById('killFeed');
        if (!killFeed) return;

        const killNotification = document.createElement('div');
        killNotification.textContent = `Killed ${zombieType}`;
        killNotification.style.marginBottom = '5px';
        killNotification.style.opacity = '1';
        killNotification.style.transition = 'opacity 0.5s';

        killFeed.appendChild(killNotification);

        // Remove after 3 seconds
        setTimeout(() => {
            killNotification.style.opacity = '0';
            setTimeout(() => {
                if (killFeed.contains(killNotification)) {
                    killFeed.removeChild(killNotification);
                }
            }, 500);
        }, 3000);
    }

    gameOver() {
        this.gameState = 'gameOver';

        // Release pointer lock
        if (document.pointerLockElement) {
            document.exitPointerLock();
        }

        // Show game over screen
        const gameOverScreen = document.getElementById('gameOverScreen');
        const finalScore = document.getElementById('finalScore');

        if (gameOverScreen && finalScore) {
            finalScore.textContent = `Final Score: ${this.score}`;
            gameOverScreen.style.display = 'flex';
        }

        // Play game over sound
        this.playSound('gameOver');
    }

    restartGame() {
        // Reset game state
        this.health = this.maxHealth;
        this.score = 0;
        this.player.position.set(0, 1.8, 0);

        // Reset weapons
        for (const weaponName in this.weapons) {
            this.weapons[weaponName].ammo = this.weapons[weaponName].maxAmmo;
        }

        // Clear game objects
        this.clearBullets();
        this.clearZombies();

        // Hide game over screen
        const gameOverScreen = document.getElementById('gameOverScreen');
        if (gameOverScreen) {
            gameOverScreen.style.display = 'none';
        }

        // Start game
        this.startGame();
    }

    clearBullets() {
        for (const bullet of this.bullets) {
            this.scene.remove(bullet);
        }
        this.bullets = [];
    }

    clearZombies() {
        for (const zombie of this.zombies) {
            this.scene.remove(zombie);
        }
        this.zombies = [];
    }
    
    render() {
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize game when page loads
window.addEventListener('load', () => {
    window.game = new ZombieGame();
});
