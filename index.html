<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zombie City Shooter - First Person Survival</title>

    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }

        canvas {
            display: block;
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div id="loadingScreen">Loading Zombie City Shooter...</div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Stats for performance monitoring -->
    <script>
        (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                stats.dom.style.position = 'absolute';
                stats.dom.style.top = '10px';
                stats.dom.style.right = '10px';
                stats.dom.style.zIndex = '200';
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop);
                });
            };
            script.src = 'https://mrdoob.github.io/stats.js/build/stats.min.js';
            document.head.appendChild(script);
        })();
    </script>

    <!-- Main Game Script -->
    <script src="game.js"></script>
</body>

</html>