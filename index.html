<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zombie Apocalypse FPS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* UI Elements */
        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #ff0000;
            border-radius: 50%;
            opacity: 0.8;
        }

        #crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: #ff0000;
            border-radius: 50%;
        }

        #hud {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #fff;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        #healthBar {
            width: 200px;
            height: 20px;
            background: rgba(255, 0, 0, 0.3);
            border: 2px solid #fff;
            margin-bottom: 10px;
            position: relative;
        }

        #healthFill {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #ff6600);
            width: 100%;
            transition: width 0.3s ease;
        }

        #ammoCounter {
            font-weight: bold;
            margin-bottom: 5px;
        }

        #weaponInfo {
            font-size: 16px;
            opacity: 0.8;
        }

        /* Start Screen */
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 200;
        }

        #gameTitle {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(255, 0, 0, 0.5);
            color: #ff3333;
        }

        #startButton {
            padding: 15px 30px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff3333, #cc0000);
            color: #fff;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            pointer-events: all;
        }

        #startButton:hover {
            background: linear-gradient(45deg, #ff5555, #ff0000);
            transform: scale(1.05);
        }

        #instructions {
            margin-top: 30px;
            text-align: center;
            font-size: 16px;
            opacity: 0.8;
            max-width: 600px;
        }

        /* Game Over Screen */
        #gameOverScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 200;
        }

        #gameOverTitle {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ff3333;
        }

        #finalScore {
            font-size: 24px;
            margin-bottom: 30px;
        }

        #restartButton {
            padding: 12px 25px;
            font-size: 20px;
            background: linear-gradient(45deg, #ff3333, #cc0000);
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            pointer-events: all;
        }

        #restartButton:hover {
            background: linear-gradient(45deg, #ff5555, #ff0000);
            transform: scale(1.05);
        }

        /* Score Display */
        #scoreDisplay {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        /* Loading Screen */
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 300;
        }

        #loadingText {
            font-size: 24px;
            margin-bottom: 20px;
        }

        #loadingBar {
            width: 300px;
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            overflow: hidden;
        }

        #loadingProgress {
            height: 100%;
            background: linear-gradient(90deg, #ff3333, #ff6600);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Mobile Controls */
        @media (max-width: 768px) {
            #mobileControls {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 200px;
                pointer-events: all;
                z-index: 150;
            }

            #joystick {
                position: absolute;
                bottom: 20px;
                left: 20px;
                width: 100px;
                height: 100px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                border: 2px solid rgba(255, 255, 255, 0.5);
            }

            #fireButton {
                position: absolute;
                bottom: 20px;
                right: 20px;
                width: 80px;
                height: 80px;
                background: rgba(255, 0, 0, 0.7);
                border-radius: 50%;
                border: 2px solid #fff;
                color: #fff;
                font-size: 16px;
                font-weight: bold;
            }
        }
    </style>
</head>

<body>
    <div id="gameContainer">
        <!-- Loading Screen -->
        <div id="loadingScreen">
            <div id="loadingText">Loading Zombie Apocalypse...</div>
            <div id="loadingBar">
                <div id="loadingProgress"></div>
            </div>
        </div>

        <!-- Start Screen -->
        <div id="startScreen">
            <h1 id="gameTitle">ZOMBIE APOCALYPSE</h1>
            <button id="startButton">START GAME</button>
            <div id="instructions">
                <p><strong>Controls:</strong></p>
                <p>WASD - Move | Mouse - Look Around | Left Click - Shoot</p>
                <p>R - Reload | 1,2,3 - Switch Weapons | ESC - Pause</p>
                <p><br>Survive the zombie apocalypse in this post-apocalyptic city!</p>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div id="gameOverScreen">
            <h2 id="gameOverTitle">GAME OVER</h2>
            <div id="finalScore">Score: 0</div>
            <button id="restartButton">RESTART</button>
        </div>

        <!-- Pause Screen -->
        <div id="pauseScreen"
            style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 200; flex-direction: column; justify-content: center; align-items: center; color: #fff;">
            <h2 style="font-size: 36px; margin-bottom: 30px;">PAUSED</h2>
            <button id="resumeButton"
                style="padding: 12px 25px; font-size: 20px; background: linear-gradient(45deg, #33ff33, #00cc00); color: #fff; border: none; border-radius: 8px; cursor: pointer; margin-bottom: 15px; pointer-events: all;">RESUME</button>
            <button id="mainMenuButton"
                style="padding: 12px 25px; font-size: 20px; background: linear-gradient(45deg, #ff3333, #cc0000); color: #fff; border: none; border-radius: 8px; cursor: pointer; pointer-events: all;">MAIN
                MENU</button>
        </div>

        <!-- Game Canvas -->
        <canvas id="gameCanvas"></canvas>

        <!-- Game UI -->
        <div id="ui">
            <div id="crosshair"></div>
            <div id="scoreDisplay">Score: 0</div>

            <div id="hud">
                <div id="healthBar">
                    <div id="healthFill"></div>
                </div>
                <div id="ammoCounter">Ammo: 30/90</div>
                <div id="weaponInfo">Assault Rifle</div>
            </div>
        </div>

        <!-- Mobile Controls -->
        <div id="mobileControls" style="display: none;">
            <div id="joystick">
                <div id="joystickKnob"
                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: rgba(255,255,255,0.8); border-radius: 50%; border: 2px solid #fff;">
                </div>
            </div>
            <div style="position: absolute; bottom: 20px; right: 20px;">
                <button id="fireButton" style="width: 80px; height: 80px; margin-bottom: 10px;">FIRE</button>
                <button id="reloadButton"
                    style="width: 60px; height: 40px; background: rgba(0,255,0,0.7); border-radius: 8px; border: 2px solid #fff; color: #fff; font-size: 12px; font-weight: bold; pointer-events: all;">RELOAD</button>
            </div>
        </div>

        <!-- Hit Indicator -->
        <div id="hitIndicator"
            style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; border: 4px solid #ff0000; border-radius: 50%; opacity: 0; pointer-events: none; z-index: 120; transition: opacity 0.1s;">
        </div>

        <!-- Kill Feed -->
        <div id="killFeed"
            style="position: absolute; top: 80px; right: 20px; color: #fff; font-size: 16px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); z-index: 110;">
            <!-- Kill notifications will appear here -->
        </div>

        <!-- Wave Indicator -->
        <div id="waveIndicator"
            style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 48px; font-weight: bold; color: #ff3333; text-shadow: 3px 3px 6px rgba(0,0,0,0.8); opacity: 0; pointer-events: none; z-index: 130; transition: opacity 0.5s;">
            WAVE 1
        </div>
    </div>

    <!-- Three.js and Game Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/PointerLockControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/TextureLoader.js"></script>

    <!-- Stats for performance monitoring -->
    <script>
        (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                stats.dom.style.position = 'absolute';
                stats.dom.style.top = '10px';
                stats.dom.style.right = '10px';
                stats.dom.style.zIndex = '200';
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop);
                });
            };
            script.src = 'https://mrdoob.github.io/stats.js/build/stats.min.js';
            document.head.appendChild(script);
        })();
    </script>

    <script src="game.js"></script>
</body>

</html>